// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "../governance/GovernanceModule.sol";
import "../governance/libraries/ProposalTypes.sol";
import "./StorageToken.sol";
import "./StoragePool.sol";
import "./StakingPool.sol";

/// @title RewardEngine
/// @notice Manages mining and storage rewards for pool members
/// @dev Inherits governance functionality from GovernanceModule
/// @dev Uses upgradeable pattern to allow for future improvements
contract RewardEngine is GovernanceModule {
    using SafeERC20 for IERC20;

    // Events
    event MiningRewardsClaimed(address indexed account, string indexed peerId, uint32 indexed poolId, uint256 amount);
    event StorageRewardsClaimed(address indexed account, string indexed peerId, uint32 indexed poolId, uint256 amount);
    event OnlineStatusSubmitted(uint32 indexed poolId, address indexed submitter, uint256 count, uint256 timestamp);
    event YearlyMiningRewardsUpdated(uint256 oldAmount, uint256 newAmount);
    event ExpectedPeriodUpdated(uint256 oldPeriod, uint256 newPeriod);
    event RewardPoolSet(address indexed stakingPool);

    // Errors
    error InvalidAmount();
    error InvalidPoolId();
    error InvalidPeerId();
    error NotPoolCreator();
    error NotPoolMember();
    error InsufficientRewards();
    error AlreadyClaimed();
    error NoRewardsToClaim();
    error InvalidTimeRange();
    error BatchTooLarge();
    error InvalidOnlineStatus();

    // Constants
    uint256 public constant MAX_BATCH_SIZE = 100;
    uint256 public constant PRECISION_FACTOR = 1e18;
    uint256 public constant SECONDS_PER_YEAR = 365 days;
    uint256 public constant DEFAULT_EXPECTED_PERIOD = 8 hours;
    uint256 public constant DEFAULT_YEARLY_MINING_REWARDS = 120_000_000 * 10**18; // 120M tokens

    // State variables
    StorageToken public token;
    StoragePool public storagePool;
    StakingPool public stakingPool;

    uint256 public yearlyMiningRewards;
    uint256 public expectedPeriod;

    // Online status tracking - optimized for minimal storage
    // poolId => peerId => account => timestamp => bool (online status)
    mapping(uint32 => mapping(string => mapping(address => mapping(uint256 => bool)))) public onlineStatus;

    // Claimed rewards tracking to prevent double claiming
    // account => peerId => poolId => lastClaimedTimestamp
    mapping(address => mapping(string => mapping(uint32 => uint256))) public lastClaimedRewards;

    // Pool creator validation cache for gas optimization
    mapping(uint32 => address) public poolCreators;

    /// @notice Initialize the RewardEngine contract
    /// @param _token Address of the StorageToken contract
    /// @param _storagePool Address of the StoragePool contract
    /// @param _stakingPool Address of the StakingPool contract (holds reward tokens)
    /// @param initialOwner Address of the initial owner
    /// @param initialAdmin Address of the initial admin
    function initialize(
        address _token,
        address _storagePool,
        address _stakingPool,
        address initialOwner,
        address initialAdmin
    ) external initializer {
        if (_token == address(0)) revert InvalidAddress();
        if (_storagePool == address(0)) revert InvalidAddress();
        if (_stakingPool == address(0)) revert InvalidAddress();
        if (initialOwner == address(0)) revert InvalidAddress();
        if (initialAdmin == address(0)) revert InvalidAddress();

        // Initialize governance module (handles UUPSUpgradeable, Ownable, ReentrancyGuard,
        // Pausable, AccessControlEnumerable, role grants, and timelocks)
        __GovernanceModule_init(initialOwner, initialAdmin);

        token = StorageToken(_token);
        storagePool = StoragePool(_storagePool);
        stakingPool = StakingPool(_stakingPool);

        yearlyMiningRewards = DEFAULT_YEARLY_MINING_REWARDS;
        expectedPeriod = DEFAULT_EXPECTED_PERIOD;

        emit RewardPoolSet(_stakingPool);
    }

    /// @notice Set the yearly mining rewards amount
    /// @param _yearlyMiningRewards New yearly mining rewards amount
    function setYearlyMiningRewards(uint256 _yearlyMiningRewards)
        external
        whenNotPaused
        nonReentrant
        onlyRole(ProposalTypes.ADMIN_ROLE)
    {
        if (_yearlyMiningRewards == 0) revert InvalidAmount();

        uint256 oldAmount = yearlyMiningRewards;
        yearlyMiningRewards = _yearlyMiningRewards;

        emit YearlyMiningRewardsUpdated(oldAmount, _yearlyMiningRewards);
    }

    /// @notice Set the expected period for online status reporting
    /// @param _expectedPeriod New expected period in seconds
    function setExpectedPeriod(uint256 _expectedPeriod)
        external
        whenNotPaused
        nonReentrant
        onlyRole(ProposalTypes.ADMIN_ROLE)
    {
        if (_expectedPeriod == 0) revert InvalidAmount();

        uint256 oldPeriod = expectedPeriod;
        expectedPeriod = _expectedPeriod;

        emit ExpectedPeriodUpdated(oldPeriod, _expectedPeriod);
    }

    /// @notice Submit online status for multiple peer IDs (batch operation)
    /// @param poolId The pool ID to submit status for
    /// @param accounts Array of member accounts
    /// @param peerIds Array of peer IDs corresponding to accounts
    /// @param timestamp The timestamp for this status update
    function submitOnlineStatusBatch(
        uint32 poolId,
        address[] calldata accounts,
        string[] calldata peerIds,
        uint256 timestamp
    ) external whenNotPaused nonReentrant {
        if (accounts.length != peerIds.length) revert InvalidOnlineStatus();
        if (accounts.length == 0 || accounts.length > MAX_BATCH_SIZE) revert BatchTooLarge();
        if (timestamp == 0 || timestamp > block.timestamp) revert InvalidTimeRange();

        // Verify caller is the pool creator
        address poolCreator = _getPoolCreator(poolId);
        if (msg.sender != poolCreator) revert NotPoolCreator();

        // Process batch updates
        for (uint256 i = 0; i < accounts.length; i++) {
            address account = accounts[i];
            string memory peerId = peerIds[i];

            if (account == address(0)) revert InvalidAddress();
            if (bytes(peerId).length == 0) revert InvalidPeerId();

            // Verify the account and peerId are members of the pool
            (bool isMember, address memberAddress) = storagePool.isPeerIdMemberOfPool(poolId, peerId);
            if (!isMember || memberAddress != account) revert NotPoolMember();

            // Record online status
            onlineStatus[poolId][peerId][account][timestamp] = true;
        }

        emit OnlineStatusSubmitted(poolId, msg.sender, accounts.length, timestamp);
    }

    /// @notice Get online status for a specific account/peerId pair since a given time
    /// @param account The member account
    /// @param peerId The peer ID
    /// @param poolId The pool ID
    /// @param sinceTime The timestamp to check from (0 for default period)
    /// @return onlineCount Number of online status records found
    /// @return totalExpected Total expected status reports in the period
    function getOnlineStatusSince(
        address account,
        string memory peerId,
        uint32 poolId,
        uint256 sinceTime
    ) external view returns (uint256 onlineCount, uint256 totalExpected) {
        if (sinceTime == 0) {
            sinceTime = block.timestamp - expectedPeriod;
        }

        if (sinceTime >= block.timestamp) revert InvalidTimeRange();

        uint256 timeRange = block.timestamp - sinceTime;
        totalExpected = timeRange / expectedPeriod;

        // Count online status records in the time range
        onlineCount = 0;
        uint256 currentTime = sinceTime;

        while (currentTime <= block.timestamp) {
            if (onlineStatus[poolId][peerId][account][currentTime]) {
                onlineCount++;
            }
            currentTime += expectedPeriod;
        }

        return (onlineCount, totalExpected);
    }

    /// @notice Calculate eligible mining rewards for a specific account/peerId pair
    /// @param account The member account
    /// @param peerId The peer ID
    /// @param poolId The pool ID
    /// @return eligibleRewards Amount of eligible mining rewards
    function calculateEligibleMiningRewards(
        address account,
        string memory peerId,
        uint32 poolId
    ) external view returns (uint256 eligibleRewards) {
        // Verify the account and peerId are members of the pool
        (bool isMember, address memberAddress) = storagePool.isPeerIdMemberOfPool(poolId, peerId);
        if (!isMember || memberAddress != account) revert NotPoolMember();

        // Get last claimed timestamp
        uint256 lastClaimed = lastClaimedRewards[account][peerId][poolId];
        uint256 sinceTime = lastClaimed > 0 ? lastClaimed : block.timestamp - expectedPeriod;

        // Get online status since last claim
        (uint256 onlineCount, uint256 totalExpected) = this.getOnlineStatusSince(account, peerId, poolId, sinceTime);

        if (onlineCount == 0 || totalExpected == 0) {
            return 0;
        }

        // Calculate base mining reward per period
        uint256 totalMembers = storagePool.getTotalMembers();
        if (totalMembers == 0) return 0;

        uint256 rewardPerMemberPerPeriod = (yearlyMiningRewards * expectedPeriod) / (SECONDS_PER_YEAR * totalMembers);

        // Calculate eligible rewards based on online ratio
        eligibleRewards = (rewardPerMemberPerPeriod * onlineCount * PRECISION_FACTOR) / (totalExpected * PRECISION_FACTOR);

        return eligibleRewards;
    }

    /// @notice Calculate eligible storage rewards for a specific account/peerId pair
    /// @param account The member account
    /// @param peerId The peer ID
    /// @param poolId The pool ID
    /// @return eligibleRewards Amount of eligible storage rewards (currently 0 as placeholder)
    function calculateEligibleStorageRewards(
        address account,
        string memory peerId,
        uint32 poolId
    ) external view returns (uint256 eligibleRewards) {
        // Verify the account and peerId are members of the pool
        (bool isMember, address memberAddress) = storagePool.isPeerIdMemberOfPool(poolId, peerId);
        if (!isMember || memberAddress != account) revert NotPoolMember();

        // Storage rewards are set to 0 as placeholder for now
        return 0;
    }

    /// @notice Get total eligible rewards (mining + storage) for a specific account/peerId pair
    /// @param account The member account
    /// @param peerId The peer ID
    /// @param poolId The pool ID
    /// @return miningRewards Amount of eligible mining rewards
    /// @return storageRewards Amount of eligible storage rewards
    /// @return totalRewards Total eligible rewards
    function getEligibleRewards(
        address account,
        string memory peerId,
        uint32 poolId
    ) external view returns (uint256 miningRewards, uint256 storageRewards, uint256 totalRewards) {
        miningRewards = this.calculateEligibleMiningRewards(account, peerId, poolId);
        storageRewards = this.calculateEligibleStorageRewards(account, peerId, poolId);
        totalRewards = miningRewards + storageRewards;

        return (miningRewards, storageRewards, totalRewards);
    }

    /// @notice Claim eligible rewards for a specific account/peerId pair
    /// @param peerId The peer ID to claim rewards for
    /// @param poolId The pool ID
    function claimRewards(
        string memory peerId,
        uint32 poolId
    ) external whenNotPaused nonReentrant {
        address account = msg.sender;

        // Verify the account and peerId are members of the pool
        (bool isMember, address memberAddress) = storagePool.isPeerIdMemberOfPool(poolId, peerId);
        if (!isMember || memberAddress != account) revert NotPoolMember();

        // Calculate eligible rewards
        (uint256 miningRewards, uint256 storageRewards, uint256 totalRewards) =
            this.getEligibleRewards(account, peerId, poolId);

        if (totalRewards == 0) revert NoRewardsToClaim();

        // Check if StakingPool has sufficient balance
        uint256 stakingPoolBalance = stakingPool.getBalance();
        if (stakingPoolBalance < totalRewards) revert InsufficientRewards();

        // Update last claimed timestamp to prevent double claiming
        lastClaimedRewards[account][peerId][poolId] = block.timestamp;

        // Transfer rewards from StakingPool
        bool success = stakingPool.transferTokens(totalRewards);
        if (!success) revert InsufficientRewards();

        // Transfer tokens to the user
        IERC20(address(token)).safeTransfer(account, totalRewards);

        // Emit events for mining and storage rewards separately
        if (miningRewards > 0) {
            emit MiningRewardsClaimed(account, peerId, poolId, miningRewards);
        }
        if (storageRewards > 0) {
            emit StorageRewardsClaimed(account, peerId, poolId, storageRewards);
        }
    }

    /// @notice Get claimed rewards information for a specific account/peerId pair
    /// @param account The member account
    /// @param peerId The peer ID
    /// @param poolId The pool ID
    /// @return lastClaimedTimestamp Timestamp of last claim
    /// @return timeSinceLastClaim Time elapsed since last claim
    function getClaimedRewardsInfo(
        address account,
        string memory peerId,
        uint32 poolId
    ) external view returns (uint256 lastClaimedTimestamp, uint256 timeSinceLastClaim) {
        lastClaimedTimestamp = lastClaimedRewards[account][peerId][poolId];
        timeSinceLastClaim = lastClaimedTimestamp > 0 ? block.timestamp - lastClaimedTimestamp : 0;

        return (lastClaimedTimestamp, timeSinceLastClaim);
    }

    /// @notice Get accumulated unclaimed rewards for a specific account/peerId pair
    /// @param account The member account
    /// @param peerId The peer ID
    /// @param poolId The pool ID
    /// @return unclaimedMining Unclaimed mining rewards
    /// @return unclaimedStorage Unclaimed storage rewards
    /// @return totalUnclaimed Total unclaimed rewards
    function getUnclaimedRewards(
        address account,
        string memory peerId,
        uint32 poolId
    ) external view returns (uint256 unclaimedMining, uint256 unclaimedStorage, uint256 totalUnclaimed) {
        return this.getEligibleRewards(account, peerId, poolId);
    }

    /// @notice Internal function to get pool creator with caching
    /// @param poolId The pool ID
    /// @return creator Address of the pool creator
    function _getPoolCreator(uint32 poolId) internal view returns (address creator) {
        // Get pool creator directly from the pools mapping
        // Note: We can't cache this in a view function, but it's a simple storage read
        (, , , , , address poolCreator, ) = storagePool.pools(poolId);
        if (poolCreator == address(0)) revert InvalidPoolId();
        return poolCreator;
    }

    /// @notice Get contract version for upgrade compatibility
    /// @return version Contract version
    function getVersion() external pure returns (uint256 version) {
        return 1;
    }

    /// @notice Authorize upgrade through governance proposal system
    function _authorizeUpgrade(address newImplementation)
        internal
        nonReentrant
        whenNotPaused
        onlyRole(ProposalTypes.ADMIN_ROLE)
        override
    {
        // Delegate the authorization to the governance module
        if (!_checkUpgrade(newImplementation)) revert("UpgradeNotAuthorized");
    }

    /// @notice Execute custom proposals for this contract
    function _executeCustomProposal(bytes32 proposalId) internal virtual override {
        ProposalTypes.UnifiedProposal storage proposal = proposals[proposalId];

        // Currently no custom proposals to execute
        // This function could be extended in the future if needed

        revert InvalidProposalType(uint8(proposal.proposalType));
    }
}
